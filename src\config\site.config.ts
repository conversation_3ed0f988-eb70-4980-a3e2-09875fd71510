import { SiteConfig } from "@/types";

export const siteConfig: SiteConfig = {
  name: "Titan",
  title: "Titan - Powerful Next.js 15 Template with Better-<PERSON>th, Drizzle ORM, PostgreSQL, and Shadcn UI",
  description: "Modern Next.js 15 stack with <PERSON>-Auth, Drizzle ORM, PostgreSQL, Shadcn UI, and Tailwind v4 for fast, secure web app development.",
  origin: "https://titan.rdsx.dev",
  keywords: [
    "Next.js 15",
    "Authentication",
    "Drizzle ORM",
    "PostgreSQL",
    "Tailwind CSS",
    "Tailwind CSS V4",
    "Shadcn UI",
    "TypeScript",
    "Full-Stack Template"
  ],
  og: "https://titan.rdsx.dev/og.png",
  creator: {
    name: "rds_agi",
    url: "https://rdsx.dev",
  },
  socials: {
    github: "https://github.com/rudrodip/titan",
    x: "https://x.com/rds_agi",
  }
}
