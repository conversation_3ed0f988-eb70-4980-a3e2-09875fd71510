{"name": "titan", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@t3-oss/env-nextjs": "^0.13.4", "better-auth": "^1.2.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "drizzle-orm": "^0.43.1", "lucide-react": "^0.503.0", "next": "15.4.0-canary.34", "next-themes": "^0.4.6", "pg": "^8.16.3", "radix-ui": "latest", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.3", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "zod": "^3.24.4"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.6", "@types/node": "^20.17.47", "@types/pg": "^8.15.4", "@types/react": "^19.1.4", "@types/react-dom": "^19.1.5", "drizzle-kit": "^0.31.1", "tailwindcss": "^4.1.6", "tw-animate-css": "^1.2.9", "typescript": "^5.8.3"}}